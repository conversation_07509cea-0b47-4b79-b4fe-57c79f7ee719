"""
Enhanced MCP Manager with Bedrock Session Management

Async-first mixin aligned with Bedrock-backed session_manager integration.
- Uses ChatSession for context/history
- Builds valid Converse toolConfig (JSON schema object)
- Persists turns to Bedrock via ChatSession.add_turn (which writes invocation steps)
"""

import logging
import os
import json
import asyncio
from typing import Dict, List, Any, Optional
from session_manager_new import session_manager

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """
    Mixin class to add Bedrock session-aware functionality to existing MCPClientManager.
    Uses native Bedrock session management (via session_manager) for context retention.
    """
    
    # Default; manager overrides from env
    model_id: str = "apac.amazon.nova-lite-v1:0"
    
    async def chat_with_bedrock_with_context(
        self,
        message: str,
        session_id: str,
        tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhanced Bedrock chat with native session context retention using session_manager.
        """
        try:
            chat_session = session_manager.get_or_create_session(session_id)

            # Check if session has already used too many tools
            session_tool_count = getattr(chat_session, 'total_tools_used', 0)
            max_session_tools = 25  # Global session limit

            if session_tool_count >= max_session_tools:
                logger.warning(f"Session {session_id} has already used {session_tool_count} tools (limit: {max_session_tools})")
                return {
                    "response": "This conversation has reached the maximum number of tool uses. Please start a new conversation to continue using tools.",
                    "tools_used": [],
                    "session_id": session_id
                }

            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user",
                "content": [{"text": message}]
            }]
            
            system_message = self._build_context_aware_system_message(chat_session, tools_available)
            tool_config = self._build_tool_config_for_bedrock(tools_available)
            
            result = await self._execute_contextual_conversation(
                messages=current_messages,
                system_message=system_message,
                tool_config=tool_config,
                session_id=session_id,
                model_id=self.model_id,
            )
            
            chat_session.add_turn(
                user_message=message,
                assistant_response=result["response"],
                tools_used=result.get("tools_used", []),
            )
            
            tools_used_count = len(result.get('tools_used', []))
            logger.info(f"Completed contextual chat for session {session_id}: {tools_used_count} tools used")

            # Update session tool count tracking
            if hasattr(chat_session, 'total_tools_used'):
                chat_session.total_tools_used += tools_used_count
            else:
                chat_session.total_tools_used = tools_used_count
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error in contextual chat for session {session_id}: {error_msg}")
            
            # Enhanced error handling
            if "Too many tools used" in error_msg:
                response_text = "I've reached the limit for tool usage in this conversation. Let me provide a response based on the information I have."
            elif "ThrottlingException" in error_msg or "Too many tokens" in error_msg:
                response_text = "I'm currently experiencing rate limits. Please wait a moment before continuing."
            elif "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Please check your BEDROCK_MODEL_ID in .env file. Current model: {self.model_id}"
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Please check your AWS_REGION configuration. Current region: {os.getenv('AWS_REGION', 'not set')}"
                else:
                    response_text = f"Bedrock API validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and region configuration."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please check your AWS permissions for Bedrock services."
            elif "ResourceNotFound" in error_msg:
                response_text = f"Resource not found. Please verify your model ID: {self.model_id}"
            else:
                response_text = f"I apologize, but I encountered an error: {error_msg}"
            
            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_context_aware_system_message(
        self,
        chat_session,
        tools_available: Optional[List[str]] = None
    ) -> str:
        """Build context-aware system message with defensive check."""
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            context = chat_session.get_context_for_bedrock()
        
        tool_hint = ""
        if tools_available:
            tool_hint = (
                "\nYou have access to tools and should use them iteratively when needed. "
                "You can batch independent tool calls in parallel. "
                "Continue using tools until you have all the information needed for a complete answer. "
                "Only stop when you are confident you can provide a final, comprehensive response."
            )
        
        # Simplified system message for Nova compatibility
        system = (
            "You are an AWS Infrastructure Monitoring Assistant. You help users understand their AWS infrastructure and costs.\n\n"
            "Key principles:\n"
            "- Use available tools to get real data\n"
            "- Never fabricate information\n"
            "- Provide clear, helpful responses\n"
            "- Default region: ap-south-1\n\n"
            "Exchange Rate: 1 USD = 85 INR (fixed for consistency)\n"
            "Format: \"$X.XX (₹Y.YY)\" - always show USD first, then INR in parentheses\n"
            "Timeframes:\n"
            "  - \"this month\" = current calendar month (month-to-date)\n"
            "  - \"last 7 days\" = rolling 7-day window\n"
            "  - \"yesterday\" = prior calendar day\n"
            "```\n\n"
            "## Response Classification System\n\n"
            "### 1. Atomic Queries\n"
            "**Pattern**: Single factual request (cost, status, count, metric)\n"
            "**Format**: `\"<Label>: <value> <unit> (<timeframe>, <region>, as of <ISO8601>)\"`\n"
            "**Length**: Exactly one line\n\n"
            "**Examples**:\n"
            "- \"What's my EC2 cost this month?\"\n"
            "- \"Is instance i-1234abcd running?\"\n"
            "- \"How much S3 storage am I using?\"\n\n"
            "**Template**:\n"
            "```\n"
            "<Service> <metric>: $<value> (₹<value_inr>) <unit> (<timeframe>, <region>, as of 2025-09-18T10:30:00Z)\n"
            "```\n\n"
            "### 2. Compute Queries\n"
            "**Pattern**: Calculations, aggregations, or analysis across multiple data points\n"
            "**Format**: Answer first, then brief computation explanation (≤2 lines)\n"
            "**Length**: Maximum 8 lines total\n\n"
            "**Examples**:\n"
            "- \"What's the average CPU utilization across my instances?\"\n"
            "- \"How many Lambda invocations happened today?\"\n"
            "- \"What's my total monthly spend so far?\"\n\n"
            "**Template**:\n"
            "```\n"
            "<Final answer with value in dual currency ($X.XX (₹Y.YY)), unit, timeframe, region, timestamp>\n"
            "<Computation method in 1-2 lines>\n"
            "```\n\n"
            "### 3. Comparison Queries\n"
            "**Pattern**: Side-by-side analysis of 2+ resources/configurations\n"
            "**Format**: Markdown table + optional 1-sentence recommendation\n"
            "**Structure**: Tailored columns for the specific comparison\n\n"
            "**Examples**:\n"
            "- \"Compare CPU usage of my production instances\"\n"
            "- \"Which region has lower costs for my workload?\"\n"
            "- \"Show me RDS vs DynamoDB pricing for my use case\"\n\n"
            "**Template**:\n"
            "```markdown\n"
            "| Column1 | Column2 | Column3 | Column4 |\n"
            "|---------|---------|---------|---------|\n"
            "| value1  | value2  | value3  | value4  |\n"
            "| value1  | value2  | value3  | value4  |\n\n"
            "[Optional: One-sentence recommendation if actionable insight exists]\n"
            "```\n\n"
            "### 4. What-If Queries\n"
            "**Pattern**: Hypothetical scenarios, scaling estimates, cost projections\n"
            "**Format**: Clear estimate + detailed calculation formula\n"
            "**Include**: Baseline vs proposed, one-time costs, ongoing monthly totals\n\n"
            "**Examples**:\n"
            "- \"If I add 2 more EC2 instances, what will it cost?\"\n"
            "- \"What if I move my workload to Graviton processors?\"\n"
            "- \"How much would it cost to replicate to another region?\"\n\n"
            "**Template**:\n"
            "```\n"
            "Scenario impact: $<cost_difference> (₹<cost_difference_inr>)\n"
            "Current: $<baseline_cost> (₹<baseline_inr>)\n"
            "Proposed: $<new_total> (₹<new_total_inr>)\n"
            "Calculation: <detailed formula>\n"
            "[Migration/one-time costs if applicable]\n"
            "Estimate: $<final_monthly_total> (₹<final_monthly_inr>) monthly\n"
            "```\n\n"
            "## Pricing & Cost Analysis Rules\n\n"
            "### Comprehensive Cost Components\n"
            "Always include relevant cost factors:\n"
            "- **Compute**: Instance hours, CPU/memory specs, OS licensing\n"
            "- **Storage**: GB stored, IOPS, throughput, storage class\n"
            "- **Network**: Data transfer in/out/inter-AZ/inter-region\n"
            "- **Load Balancing**: ALB/NLB LCUs, NAT Gateway hours\n"
            "- **Monitoring**: CloudWatch logs/metrics, custom metrics\n"
            "- **Backup**: Snapshots, automated backups, cross-region replication\n"
            "- **Support**: Business/Enterprise support plans when material (>2% of total cost)\n\n"
            "### Discount Application\n"
            "- **Detected Savings Plans/RIs**: Apply automatically, note coverage percentage\n"
            "- **Unknown discount status**: Show both On-Demand and potential discounted scenarios\n"
            "- **Estimation**: Clearly mark with \"Estimate:\" prefix when uncertain\n\n"
            "### Cost Accuracy Standards\n"
            "- **Currency Format**: Always show dual pricing: \"$X.XX (₹Y.YY)\" using 1 USD = 85 INR conversion\n"
            "- **Rounding**: Round to 2 decimals, use thousands separators for large numbers\n"
            "- **Validation**: Cross-check Cost Explorer vs CUR when possible\n"
            "- **Variance handling**: If sources differ by >5%, prefer CUR and note \"values reconciled to CUR\"\n"
            "- **Conversion Logic**: If source data is in INR, divide by 85 for USD; if in USD, multiply by 85 for INR\n\n"
            "## Performance Optimization\n\n"
            "### Tool Usage\n"
            "- **Parallel calls**: Execute multiple MCP tools simultaneously when safe\n"
            "- **Caching**: Store identical queries for 60 seconds to reduce latency\n"
            "- **Timeout handling**: If tools don't respond within 30s, acknowledge delay and provide partial answer if possible\n\n"
            "### Response Efficiency\n"
            "- **Atomic**: Immediate one-line response\n"
            "- **Complex**: Answer first, methodology second\n"
            "- **Tables**: Clean markdown, no unnecessary columns\n"
            "- **Timestamps**: Always ISO8601 format in UTC\n\n"
            "## Error Handling Patterns\n\n"
            "### Common Scenarios & Responses\n\n"
            "**Insufficient Data**:\n"
            "```\n"
            "\"I don't have enough data from the tools to answer this.\"\n"
            "```\n\n"
            "**Permission Denied**:\n"
            "```\n"
            "\"I don't have enough data from the tools to answer this. MissingPermission: ec2:DescribeInstances\"\n"
            "```\n\n"
            "**Ambiguous Input** (auto-resolve with defaults):\n"
            "```\n"
            "\"<Answer using defaults> (assumed ap-south-1 region, current month timeframe)\"\n"
            "```\n\n"
            "**Partial Data Available**:\n"
            "```\n"
            "\"<Partial answer>. Note: Some data unavailable due to [specific limitation].\"\n"
            "```\n\n"
            "## Quality Assurance Checklist\n\n"
            "Before responding, verify:\n"
            "- [ ] Data sourced from tools (no fabrication)\n"
            "- [ ] Sensitive data properly masked\n"
            "- [ ] Units, timeframe, region included\n"
            "- [ ] ISO8601 timestamp when applicable\n"
            "- [ ] Currency formatted as \"$X.XX (₹Y.YY)\" using 1 USD = 85 INR\n"
            "- [ ] Response matches query classification\n"
            "- [ ] Calculation formulas shown for estimates\n"
            "- [ ] Source hierarchy respected\n\n"
            "## Response Examples\n\n"
            "### Atomic Example\n"
            "**Query**: \"What's my current EC2 spend?\"\n"
            "**Response**:\n"
            "```\n"
            "EC2 cost: $145.27 (₹12,347.95) (month-to-date, ap-south-1, as of 2025-09-18T10:30:00Z)\n"
            "```\n\n"
            "### Compute Example\n"
            "**Query**: \"What's the average CPU utilization across my instances?\"\n"
            "**Response**:\n"
            "```\n"
            "Average EC2 CPU utilization: 42% (last 7 days, ap-south-1, as of 2025-09-18T10:30:00Z)\n"
            "Computed by fetching CloudWatch CPUUtilization for 5 running instances and averaging datapoints.\n"
            "```\n\n"
            "### Comparison Example\n"
            "**Query**: \"Compare my production instances\"\n"
            "**Response**:\n"
            "```markdown\n"
            "| Instance ID | Type      | CPU | Memory | Region    | Cost/month        |\n"
            "|-------------|-----------|-----|--------|-----------|-------------------|\n"
            "| i-****abcd  | m5.large  | 35% | 2.1 GB | us-east-1 | $28.40 (₹2,414)   |\n"
            "| i-****efgh  | m5.xlarge | 72% | 6.5 GB | us-west-2 | $56.80 (₹4,828)   |\n\n"
            "Recommendation: i-****efgh shows higher utilization, consider monitoring for potential scaling needs.\n"
            "```\n\n"
            "### What-If Example\n"
            "**Query**: \"If I add 2 m5.large instances running 24/7, what's the cost?\"\n"
            "**Response**:\n"
            "```\n"
            "Additional monthly cost: $138.24 (₹11,750.40)\n"
            "Current EC2 spend: $234.56 (₹19,937.60)\n"
            "New total: $372.80 (₹31,688.00)\n"
            "Calculation: $0.096 × 24 hours × 30 days × 2 instances = $138.24\n"
            "Estimate: Based on us-east-1 On-Demand pricing\n"
            "```\n\n"
            "## Final Instructions\n\n"
            "1. **Never mention the classification category** (Atomic, Compute, Comparison, What-if) in your response\n"
            "2. **Always prioritize accuracy over speed** - better to say \"I don't have data\" than guess\n"
            "3. **Keep responses concise but complete** - include all necessary context without verbosity\n"
            "4. **Format consistently** - users should be able to parse responses programmatically\n"
            "5. **When in doubt about permissions or data availability, explicitly state limitations**"
            f"\n\nSession Context:\n{context}"
            f"{tool_hint}"
        )
        
        return system

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Optional[Dict]:
        """Build Nova-compliant tool configuration for Bedrock."""
        if not tools_available:
            return None

        available_tools = self.get_available_tools()
        tools: List[Dict[str, Any]] = []
        # Create mapping from cleaned names back to original tool keys
        self._tool_name_mapping = {}

        for tool_key in tools_available:
            if tool_key not in available_tools:
                logger.warning(f"Tool {tool_key} not in available tools")
                continue

            tool_data = available_tools[tool_key]
            tool = tool_data["tool"]

            # Nova requires strict schema compliance
            input_schema = tool.get("input_schema", {})

            # Ensure top-level schema compliance
            if not isinstance(input_schema, dict):
                input_schema = {"type": "object", "properties": {}}

            # Remove unsupported fields at top level and ensure Nova compliance
            nova_schema = {
                "type": "object",  # Nova requires object type
                "properties": input_schema.get("properties", {}),
            }

            # Only add 'required' if it exists and is not empty
            if "required" in input_schema and input_schema["required"]:
                nova_schema["required"] = input_schema["required"]

            # Ensure all properties have proper types
            for prop_name, prop_def in nova_schema["properties"].items():
                if isinstance(prop_def, dict) and "type" not in prop_def:
                    prop_def["type"] = "string"  # Default to string if no type specified

            # Nova requires clean tool names (alphanumeric + underscores only)
            clean_tool_name = tool_key.replace("::", "_").replace("-", "_")
            # Ensure it starts with a letter
            if not clean_tool_name[0].isalpha():
                clean_tool_name = f"tool_{clean_tool_name}"

            # Store mapping for later lookup
            self._tool_name_mapping[clean_tool_name] = tool_key

            tools.append({
                "toolSpec": {
                    "name": clean_tool_name,
                    "description": tool.get("description", f"Tool from server {tool_data['server']}"),
                    "inputSchema": {"json": nova_schema}
                }
            })

        if not tools:
            return None

        # Nova-specific tool choice configuration
        tool_choice = {"auto": {}}  # Try auto first for Nova

        return {
            "tools": tools,
            "toolChoice": tool_choice
        }

    async def _execute_contextual_conversation(
        self,
        messages: List[Dict[str, Any]],
        system_message: str,
        tool_config: Optional[Dict],
        session_id: str,
        model_id: str,
    ) -> Dict[str, Any]:
        """Execute contextual conversation with multi-iteration tool loop."""
        runtime = await self.get_async_bedrock_runtime()
        # Nova models require temperature 0 for reliable tool calling
        inference_config = {
            "temperature": 0,      # Critical for Nova tool calling
            "topP": 0.9,
            "maxTokens": 2048      # Reduced for Nova compatibility
        }
        
        msgs = list(messages)
        tools_used: List[Dict[str, Any]] = []
        max_iterations = 8  # Reduced to prevent throttling
        max_tools_per_session = 15  # Limit total tools per conversation
        consecutive_tool_failures = 0  # Track consecutive failures
        max_consecutive_failures = 3  # Stop after 3 consecutive failures
        
        for iteration in range(max_iterations):
            # Check if we've exceeded tool usage limits
            if len(tools_used) >= max_tools_per_session:
                logger.warning(f"Stopping conversation: reached max tools limit ({max_tools_per_session})")
                final_response = "I've used the maximum number of tools allowed for this conversation. Let me provide a summary based on the information gathered so far."
                return {"response": final_response, "tools_used": tools_used, "session_id": session_id}

            # Check for consecutive failures
            if consecutive_tool_failures >= max_consecutive_failures:
                logger.warning(f"Stopping conversation: {consecutive_tool_failures} consecutive tool failures")
                final_response = "I'm experiencing difficulties with tool execution. Let me provide a response based on the information I have."
                return {"response": final_response, "tools_used": tools_used, "session_id": session_id}

            try:
                req = {
                    "modelId": model_id,
                    "messages": msgs,
                    "system": [{"text": system_message}],
                    "inferenceConfig": inference_config,
                }

                # Nova-specific additional fields for better tool calling
                if "nova" in model_id.lower():
                    req["additionalModelRequestFields"] = {
                        "inferenceConfig": {
                            "topK": 1,
                            "stopSequences": []  # Ensure no stop sequences interfere
                        }
                    }

                if tool_config:
                    req["toolConfig"] = tool_config
                    # Log tool config for debugging Nova issues
                    logger.debug(f"Tool config for Nova: {json.dumps(tool_config, indent=2)}")

                logger.debug(f"Sending request to Nova model: {model_id}")
                resp = await runtime.converse(**req)
                output = resp.get("output", {}).get("message", {})
                content = output.get("content", [])
                stop_reason = resp.get("stopReason")
                
                logger.debug(f"Iteration {iteration + 1}: stop_reason={stop_reason}")
                
                if stop_reason == "tool_use":
                    # Record assistant toolUse
                    msgs.append({"role": "assistant", "content": content})
                    
                    # Execute tool calls in parallel
                    planned_calls = []
                    for b in content:
                        if "toolUse" in b:
                            tool_use = b["toolUse"]
                            # Map cleaned tool name back to original key
                            clean_name = tool_use["name"]
                            original_key = getattr(self, '_tool_name_mapping', {}).get(clean_name, clean_name)
                            tool_call = {
                                "key": original_key,
                                "input": tool_use.get("input", {}),
                                "toolUseId": tool_use["toolUseId"],
                            }
                            planned_calls.append(tool_call)
                            logger.debug(f"Planned tool call: {tool_call}")

                    logger.info(f"Executing {len(planned_calls)} tool calls in parallel")

                    exec_results = await self._execute_tool_calls(planned_calls, session_id)
                    tools_used.extend(exec_results)

                    # Check tool execution success rate
                    successful_tools = sum(1 for result in exec_results if result.get("success", False))
                    failed_tools = len(exec_results) - successful_tools

                    if failed_tools == len(exec_results):
                        # All tools failed
                        consecutive_tool_failures += 1
                        logger.warning(f"All {len(exec_results)} tools failed in iteration {iteration + 1}")
                    else:
                        # At least one tool succeeded, reset failure counter
                        consecutive_tool_failures = 0
                        if failed_tools > 0:
                            logger.info(f"Tool execution: {successful_tools} succeeded, {failed_tools} failed")
                    
                    # Provide toolResult blocks back to model as text (not JSON)
                    tool_result_blocks = []
                    for call, res in zip(planned_calls, exec_results):
                        if res.get("success"):
                            result_data = res.get("result", "")
                            # Convert result to string format for Bedrock
                            if isinstance(result_data, (dict, list)):
                                tool_response_text = json.dumps(result_data, indent=2)
                            else:
                                tool_response_text = str(result_data)
                        else:
                            error_msg = res.get("error", "Unknown error")
                            tool_response_text = f"Error: {error_msg}"

                        tool_result_blocks.append({
                            "toolResult": {
                                "toolUseId": call["toolUseId"],
                                "content": [{"text": tool_response_text}]
                            }
                        })
                    
                    msgs.append({"role": "user", "content": tool_result_blocks})

                    # Add a small delay between iterations to prevent throttling
                    if iteration > 0:  # Don't delay on first iteration
                        await asyncio.sleep(0.5)  # 500ms delay

                    continue
                
                elif stop_reason in ("end_turn", "stop_sequence", "max_tokens"):
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else ""
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}

                else:
                    final_text_parts = [b["text"] for b in content if "text" in b]
                    final_text = "\n".join(final_text_parts).strip() if final_text_parts else f"Response completed with stop reason: {stop_reason}"
                    # Filter out thinking content before returning
                    filtered_text = self._filter_thinking_content(final_text)
                    return {"response": filtered_text, "tools_used": tools_used, "session_id": session_id}
                    
            except Exception as e:
                error_str = str(e)
                logger.error(f"Error in conversation iteration {iteration + 1}: {error_str}")

                # Handle specific AWS throttling errors
                if "ThrottlingException" in error_str or "Too many tokens" in error_str:
                    logger.warning("AWS throttling detected, stopping conversation to prevent further rate limiting")
                    return {
                        "response": "I've reached the rate limit for API calls. Please wait a moment before continuing the conversation.",
                        "tools_used": tools_used,
                        "session_id": session_id
                    }

                # Handle tool usage limits
                if "Too many tools used" in error_str:
                    logger.warning("Tool usage limit exceeded")
                    return {
                        "response": "I've used too many tools in this conversation. Let me provide a summary based on the information gathered.",
                        "tools_used": tools_used,
                        "session_id": session_id
                    }

                if iteration == 0:  # If first iteration fails, return error
                    raise
                # Otherwise, return what we have so far
                return {"response": f"Partial response due to error: {error_str}", "tools_used": tools_used, "session_id": session_id}
        
        # If we reach here, we've hit max iterations
        logger.warning(f"Conversation reached maximum iterations ({max_iterations}) with {len(tools_used)} tools used")
        return {"response": "Response completed after maximum iterations.", "tools_used": tools_used, "session_id": session_id}

    def _check_tool_usage_limits(self, session_id: str, current_tools_used: int) -> Optional[str]:
        """Check if tool usage limits have been exceeded and return appropriate message."""
        max_tools_per_conversation = 15
        max_tools_per_session = 25

        if current_tools_used >= max_tools_per_conversation:
            logger.error(f"Error in contextual chat for session {session_id}: Too many tools used: {current_tools_used}")
            return "I've used too many tools in this conversation. Let me provide a summary based on the information gathered."

        # You could add session-level checking here if you track it
        return None

    def _filter_thinking_content(self, text: str) -> str:
        """
        Filter out thinking content from model responses.
        Removes content between <thinking> and </thinking> tags and other reasoning patterns.
        """
        import re

        if not text:
            return text

        # Remove thinking tags and their content
        # This pattern matches <thinking>...</thinking> including multiline content
        thinking_pattern = r'<thinking>.*?</thinking>'
        filtered_text = re.sub(thinking_pattern, '', text, flags=re.DOTALL | re.IGNORECASE)

        # Also remove other common reasoning patterns that might leak through
        # Remove content between <reasoning> and </reasoning> tags
        reasoning_pattern = r'<reasoning>.*?</reasoning>'
        filtered_text = re.sub(reasoning_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Remove content between <analysis> and </analysis> tags
        analysis_pattern = r'<analysis>.*?</analysis>'
        filtered_text = re.sub(analysis_pattern, '', filtered_text, flags=re.DOTALL | re.IGNORECASE)

        # Clean up any extra whitespace that might be left
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)  # Replace multiple newlines with double newlines
        filtered_text = filtered_text.strip()

        return filtered_text

    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client."""
        try:
            import aioboto3
        except ImportError:
            raise ImportError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")
        
        session = aioboto3.Session()
        client = session.client("bedrock-runtime")
        return await client.__aenter__()

    def _parse_tool_key(self, tool_key: str) -> tuple[str, str]:
        """Enhanced tool key parsing with better error handling."""
        if not tool_key:
            raise ValueError("Tool key is empty")

        # Direct tool name lookup first
        available_tools = self.get_available_tools()
        if tool_key in available_tools:
            tool_data = available_tools[tool_key]
            return tool_data["server"], tool_data["tool"]["name"]

        # Fallback to :: separator parsing
        if "::" in tool_key:
            parts = tool_key.split("::", 1)
            if len(parts) == 2:
                return parts[0], parts[1]

        # Last resort: search by tool name across all servers
        for _, tool_data in available_tools.items():
            if tool_data["tool"]["name"] == tool_key:
                return tool_data["server"], tool_key

        raise ValueError(f"Tool '{tool_key}' not found in available tools: {list(available_tools.keys())}")

    def debug_available_tools(self):
        """Debug method to inspect available tools."""
        available_tools = self.get_available_tools()
        logger.info(f"Available tools: {list(available_tools.keys())}")
        for key, data in available_tools.items():
            logger.info(f"  {key}: server={data['server']}, name={data['tool']['name']}")
        return available_tools

    async def _execute_tool_calls(self, tool_calls: List[Dict], session_id: str) -> List[Dict]:
        """Execute tool calls with enhanced Nova compatibility."""
        tools_used = []

        # Debug available tools first
        available_tools = self.get_available_tools()
        logger.info(f"Available tools for session {session_id}: {list(available_tools.keys())}")

        for tool_call in tool_calls:
            tool_key = tool_call.get("key")
            tool_input = tool_call.get("input", {})
            tool_use_id = tool_call.get("toolUseId")

            logger.debug(f"Processing tool call: key='{tool_key}', input={tool_input}")

            try:
                # Enhanced tool key parsing
                server_name, tool_name = self._parse_tool_key(tool_key)
                logger.info(f"Executing tool: {tool_name} on server: {server_name}")

                result = await self.call_tool(server_name, tool_name, tool_input)

                usage = {
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "input": tool_input,
                    "success": result.get("success", False),
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                }

                if usage["success"]:
                    usage["result"] = str(result.get("result", ""))
                else:
                    usage["error"] = result.get("error", "Unknown error")

                tools_used.append(usage)

            except Exception as e:
                logger.error(f"Tool execution exception for {tool_key}: {e}")
                tools_used.append({
                    "tool_name": tool_key,
                    "server_name": "unknown",
                    "input": tool_input,
                    "success": False,
                    "error": f"Parsing/execution error: {str(e)}",
                    "session_id": session_id,
                    "toolUseId": tool_use_id,
                })

        return tools_used

