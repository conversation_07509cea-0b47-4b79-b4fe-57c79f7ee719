"""
Enhanced Session Manager with Bedrock Integration - BEDROCK ONLY MODE with Session Discovery

Provides robust session management with AWS Bedrock Agent Runtime support and session discovery
"""

import os
import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional
from uuid import uuid4
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)

class ConversationTurn:
    """Represents a single conversation turn with user message and assistant response."""
    
    def __init__(
        self,
        timestamp: datetime,
        user_message: str,
        assistant_response: str,
        tools_used: Optional[List[Dict[str, Any]]] = None,
        session_id: Optional[str] = None,
    ):
        self.timestamp = timestamp
        self.user_message = user_message
        self.assistant_response = assistant_response
        self.tools_used = tools_used or []
        self.session_id = session_id

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "timestamp": self.timestamp.isoformat(),
            "user_message": self.user_message,
            "assistant_response": self.assistant_response,
            "tools_used": self.tools_used,
            "session_id": self.session_id,
        }

class SessionConfig:
    """Configuration for session management."""
    
    def __init__(
        self,
        region_name: Optional[str] = None,
        encryption_key_arn: Optional[str] = None,
        session_timeout_hours: int = 2,
        max_conversation_turns: int = 200,
        max_tools_per_turn: int = 25,
        enable_cleanup: bool = True,
        delete_on_cleanup: bool = True,
        recovery_max_steps: int = 200,
        compress_keep_recent: int = 20,
        max_user_message_len: int = 20000,
        max_assistant_response_len: int = 100000,
        max_metadata_len: int = 5000,
        persist_tools_minimal: bool = True,
    ):
        self.region_name = region_name or os.getenv("AWS_REGION", "ap-south-1")
        self.encryption_key_arn = encryption_key_arn
        self.session_timeout_hours = session_timeout_hours
        self.max_conversation_turns = max_conversation_turns
        self.max_tools_per_turn = max_tools_per_turn
        self.enable_cleanup = enable_cleanup
        self.delete_on_cleanup = delete_on_cleanup
        self.recovery_max_steps = recovery_max_steps
        self.compress_keep_recent = compress_keep_recent
        self.max_user_message_len = max_user_message_len
        self.max_assistant_response_len = max_assistant_response_len
        self.max_metadata_len = max_metadata_len
        self.persist_tools_minimal = persist_tools_minimal

    def validate(self):
        """Validate configuration values."""
        if self.session_timeout_hours <= 0:
            raise ValueError("session_timeout_hours must be positive")
        if self.max_conversation_turns <= 0:
            raise ValueError("max_conversation_turns must be positive")
        if self.max_tools_per_turn <= 0:
            raise ValueError("max_tools_per_turn must be positive")

class BedrockSessionClient:
    """AWS Bedrock Agent Runtime client for session management with discovery capabilities."""
    
    def __init__(
        self,
        region_name: str,
        encryption_key_arn: Optional[str] = None,
        session_metadata: Optional[Dict[str, str]] = None,
    ):
        self.region_name = region_name
        self.encryption_key_arn = encryption_key_arn
        self.session_metadata = session_metadata or {}
        
        # Initialize Bedrock Agent Runtime client
        try:
            import boto3
            self.client = boto3.client(
                "bedrock-agent-runtime",
                region_name=region_name,
            )
            logger.info(f"Bedrock Agent Runtime client initialized for region {region_name}")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock Agent Runtime client: {e}")
            raise

    def create_session(self) -> str:
        """Create a new Bedrock session."""
        try:
            params = {"sessionMetadata": self.session_metadata}
            if self.encryption_key_arn:
                params["encryptionKeyArn"] = self.encryption_key_arn
            
            response = self.client.create_session(**params)
            session_id = response["sessionId"]
            logger.info(f"Created Bedrock session: {session_id}")
            return session_id
        except Exception as e:
            logger.error(f"Failed to create Bedrock session: {e}")
            raise

    def get_session(self, session_identifier: str) -> Dict[str, Any]:
        """Get session details."""
        return self.client.get_session(sessionIdentifier=session_identifier)

    def list_all_sessions(self, max_results: int = 50) -> List[Dict[str, Any]]:
        """List all sessions in the AWS account."""
        try:
            response = self.client.list_sessions(maxResults=max_results)
            sessions = response.get("sessionSummaries", [])
            logger.info(f"Found {len(sessions)} sessions in account")
            return sessions
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            return []

    def search_sessions_by_metadata(self, search_term: str) -> List[Dict[str, Any]]:
        """Search sessions by metadata (app name, user, etc.)."""
        all_sessions = self.list_all_sessions()
        matching_sessions = []
        
        for session_summary in all_sessions:
            try:
                # Get full session details
                session_details = self.get_session(session_summary["sessionId"])
                metadata = session_details.get("sessionMetadata", {})
                
                # Search in metadata values
                if any(search_term.lower() in str(value).lower() 
                       for value in metadata.values()):
                    matching_sessions.append({
                        **session_summary,
                        "metadata": metadata
                    })
            except Exception as e:
                logger.warning(f"Failed to get details for session {session_summary.get('sessionId')}: {e}")
        
        return matching_sessions

    def delete_session(self, session_id: str):
        """Delete a Bedrock session."""
        try:
            self.client.delete_session(sessionIdentifier=session_id)
            logger.info(f"Deleted Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to delete Bedrock session {session_id}: {e}")
            raise

    def end_session(self, session_id: str):
        """End a Bedrock session."""
        try:
            self.client.end_session(sessionIdentifier=session_id)
            logger.info(f"Ended Bedrock session: {session_id}")
        except Exception as e:
            logger.error(f"Failed to end Bedrock session {session_id}: {e}")
            raise

    def create_invocation(self, session_identifier: str, description: str) -> str:
        """Create an invocation within a session."""
        try:
            response = self.client.create_invocation(
                sessionIdentifier=session_identifier,
                description=description,
            )
            invocation_id = response["invocationId"]
            logger.info(f"Created invocation {invocation_id} for session {session_identifier}")
            return invocation_id
        except Exception as e:
            logger.error(f"Failed to create invocation for session {session_identifier}: {e}")
            raise

    def put_invocation_step_structured(
        self,
        session_identifier: str,
        invocation_identifier: str,
        role: str,
        text: str,
        tools_used: List[Dict[str, Any]],
        timestamp: datetime,
        persist_tools_minimal: bool,
        max_metadata_len: int,
    ):
        """Put a structured invocation step with contentBlocks payload."""
        try:
            blocks: List[Dict[str, Any]] = []
            
            # Add main text
            if text:
                blocks.append({"text": text})
            
            # Add metadata
            meta = {"role": role, "timestamp": timestamp.isoformat()}
            meta_s = json.dumps(meta)
            if len(meta_s) <= max_metadata_len:
                blocks.append({"text": f"[METADATA]{meta_s}"})
            
            # Add tools if not minimal
            if tools_used and not persist_tools_minimal:
                tools_s = json.dumps({"tools": tools_used})
                if len(tools_s) <= max_metadata_len:
                    blocks.append({"text": f"[TOOLS]{tools_s}"})
            
            payload = {"contentBlocks": blocks}
            
            self.client.put_invocation_step(
                sessionIdentifier=session_identifier,
                invocationIdentifier=invocation_identifier,
                invocationStepId=str(uuid4()),
                invocationStepTime=timestamp,
                payload=payload,
            )
            logger.debug(f"Put invocation step for session {session_identifier}")
        except Exception as e:
            logger.error(f"Failed to put invocation step: {e}")
            raise

    def list_invocation_steps(
        self,
        session_identifier: str,
        max_steps: int = 50,
        next_token: Optional[str] = None,
    ) -> tuple[List[Dict[str, Any]], Optional[str]]:
        """List invocation steps for a session."""
        try:
            params: Dict[str, Any] = {
                "sessionIdentifier": session_identifier,
                "maxResults": max_steps,
            }
            if next_token:
                params["nextToken"] = next_token
            
            response = self.client.list_invocation_steps(**params)
            steps = response.get("invocationStepSummaries", [])
            next_token = response.get("nextToken")
            logger.debug(f"Listed {len(steps)} invocation steps for session {session_identifier}")
            return steps, next_token
        except Exception as e:
            logger.error(f"Failed to list invocation steps for session {session_identifier}: {e}")
            return [], None

    def get_invocation_step(
        self,
        session_identifier: str,
        invocation_identifier: str,
        invocation_step_id: str,
    ) -> Dict[str, Any]:
        """Get a specific invocation step."""
        try:
            return self.client.get_invocation_step(
                sessionIdentifier=session_identifier,
                invocationIdentifier=invocation_identifier,
                invocationStepId=invocation_step_id,
            )
        except Exception as e:
            logger.error(f"Failed to get invocation step {invocation_step_id}: {e}")
            return {}

class ChatSession:
    """
    Manages individual chat session with conversation history and context,
    persisted via Bedrock sessions and locally cached for quick access.
    """
    
    def __init__(
        self,
        session_id: Optional[str] = None,
        bedrock_backend: Optional["BedrockSessionClient"] = None,
        invocation_description: Optional[str] = "mcp-bot conversation",
        config: Optional["SessionConfig"] = None,
        require_bedrock: bool = True,
        existing_bedrock_session_id: Optional[str] = None,  # New parameter for restoration
    ):
        self.config = config or SessionConfig()
        self.config.validate()
        self.client_session_id = session_id or f"bedrock_{str(uuid4())[:8]}"
        self._is_closed = False
        
        if require_bedrock:
            if not bedrock_backend:
                raise ValueError("Bedrock backend is REQUIRED but not provided")
            
            self.bedrock = bedrock_backend
            
            try:
                # Use existing session ID if provided (for restoration)
                if existing_bedrock_session_id:
                    self.bedrock_session_id = existing_bedrock_session_id
                    logger.info(f"✅ Restored existing Bedrock session: {self.bedrock_session_id}")
                else:
                    self.bedrock_session_id = self.bedrock.create_session()
                    logger.info(f"✅ Created new Bedrock session: {self.bedrock_session_id}")
                
                self.use_bedrock_sessions = True
            except Exception as e:
                logger.error(f"❌ CRITICAL: Failed to create/restore required Bedrock session: {e}")
                raise RuntimeError(f"Required Bedrock session creation/restoration failed: {e}") from e
        else:
            # Hybrid mode (keep for backward compatibility)
            self.use_bedrock_sessions = False
            self.bedrock: Optional[BedrockSessionClient] = None
            self.bedrock_session_id: Optional[str] = None
            
            if bedrock_backend:
                self.bedrock = bedrock_backend
                try:
                    self.bedrock_session_id = self.bedrock.create_session()
                    self.use_bedrock_sessions = True
                except Exception as e:
                    logger.warning(f"Bedrock session management not available, using local-only mode: {e}")
                    self.use_bedrock_sessions = False
        
        # Legacy alias
        self.session_id = self.client_session_id
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)
        self.conversation_history: List[ConversationTurn] = []
        self.context_summary = ""
        self.total_tools_used = 0
        self._last_summary_turn_count = 0
        
        # Create invocation only if Bedrock session is active
        self.current_invocation_id: Optional[str] = None
        if self.use_bedrock_sessions:
            # Handle existing vs new sessions differently
            if existing_bedrock_session_id:
                # For restored sessions, try to find existing invocation first
                logger.info(f"🔄 Restoring session - checking for existing invocations")
                try:
                    steps, _ = self.bedrock.list_invocation_steps(
                        session_identifier=self.bedrock_session_id,
                        max_steps=1
                    )
                    if steps:
                        self.current_invocation_id = steps[0].get("invocationIdentifier")
                        logger.info(f"✅ Found existing invocation: {self.current_invocation_id}")
                except Exception as e:
                    logger.warning(f"Could not find existing invocation, will create new one: {e}")

            # Create new invocation only if needed
            if not self.current_invocation_id:
                logger.info(f"🔄 Creating new invocation for session {self.bedrock_session_id}")
                try:
                    self.current_invocation_id = self.bedrock.create_invocation(
                        session_identifier=self.bedrock_session_id,
                        description=invocation_description,
                    )
                    logger.info(f"✅ Invocation created successfully: {self.current_invocation_id}")
                except Exception as e:
                    logger.error(f"❌ CRITICAL: Failed to create invocation: {e}")
                    raise RuntimeError(f"Required Bedrock invocation creation failed: {e}") from e

            # ✅ FIX: Move load_from_bedrock() to INSIDE the Bedrock-enabled block
            # Attempt warm recovery from Bedrock with enhanced debugging
            try:
                self.load_from_bedrock(max_steps=self.config.recovery_max_steps)
                logger.info(f"After load_from_bedrock: conversation history has {len(self.conversation_history)} turns")

                if len(self.conversation_history) == 0:
                    logger.warning("Conversation history is empty after Bedrock recovery - investigating...")
                    # Call debug method if history is empty
                    if hasattr(self, 'debug_bedrock_session'):
                        self.debug_bedrock_session(self.bedrock_session_id)
            except Exception as e2:
                logger.error(f"load_from_bedrock failed with error: {e2}")
                logger.error(f"Exception type: {type(e2).__name__}")
        else:
            logger.info(f"ℹ️  Bedrock sessions disabled, skipping invocation creation and recovery")

    def get_bedrock_messages(self, max_turns: int = 10) -> List[Dict[str, Any]]:
        """Convert conversation history to Bedrock message format."""
        messages: List[Dict[str, Any]] = []
        recent = self.conversation_history[-max_turns:]
        for turn in recent:
            messages.append({"role": "user", "content": [{"text": turn.user_message}]})
            messages.append({"role": "assistant", "content": [{"text": turn.assistant_response}]})
        return messages

    def get_context_for_bedrock(self) -> str:
        """Compose a concise session context summary for system prompt."""
        parts: List[str] = []
        parts.append(f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}")
        parts.append(f"Total conversation turns: {len(self.conversation_history)}")
        parts.append(f"Total tools executed: {self.total_tools_used}")
        
        if self.context_summary:
            parts.append(f"Recent context: {self.context_summary}")
        
        # Recent successful tool names
        recent_tools: List[str] = []
        recent_servers: set = set()
        for turn in self.conversation_history[-3:]:
            for t in turn.tools_used:
                if t.get("success"):
                    if t.get("tool_name"):
                        recent_tools.append(t["tool_name"])
                    if t.get("server_name"):
                        recent_servers.add(t["server_name"])
        
        if recent_tools:
            parts.append(f"Recently used tools: {', '.join(sorted(set(recent_tools)))}")
        if recent_servers:
            parts.append(f"Active servers: {', '.join(sorted(recent_servers))}")
        
        return "\n".join(parts)

    def _validate_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]]):
        """Validate turn inputs."""
        if not user_message or not user_message.strip():
            raise ValueError("user_message cannot be empty")
        if not assistant_response or not assistant_response.strip():
            raise ValueError("assistant_response cannot be empty")
        if len(user_message) > self.config.max_user_message_len:
            raise ValueError("user_message too long")
        if len(assistant_response) > self.config.max_assistant_response_len:
            raise ValueError("assistant_response too long")
        if tools_used and len(tools_used) > self.config.max_tools_per_turn:
            raise ValueError(f"Too many tools used: {len(tools_used)}")

    def _extract_topics_fast(self, text: str) -> List[str]:
        """Fast topic extraction for context summarization."""
        topics: List[str] = []
        low = text.lower()
        if any(k in low for k in ("budget", "cost", "billing")):
            topics.append("cost_analysis")
        if any(k in low for k in ("cloudformation", "stack", "template")):
            topics.append("infrastructure")
        if any(k in low for k in ("pricing", "price", "compare")):
            topics.append("pricing_analysis")
        return topics

    def _update_context_summary_optimized(self):
        """Lightweight rolling summary of the last few turns."""
        if len(self.conversation_history) < 2:
            return
        
        recent = self.conversation_history[-3:]
        topics = set()
        for t in recent:
            topics.update(self._extract_topics_fast(t.user_message))
        
        self.context_summary = f"Topics: {','.join(sorted(topics))}" if topics else "General conversation"

    def _create_compressed_summary(self, turns: List[ConversationTurn]) -> str:
        """Create compressed summary of old turns."""
        counts = {"cost_analysis": 0, "infrastructure": 0, "pricing_analysis": 0}
        for t in turns:
            for tp in self._extract_topics_fast(t.user_message):
                if tp in counts:
                    counts[tp] += 1
        
        last_user = turns[-1].user_message[:120].replace("\n", " ") if turns else ""
        last_assistant = turns[-1].assistant_response[:120].replace("\n", " ") if turns else ""
        
        return f"topics={counts}, last_user='{last_user}...', last_assistant='{last_assistant}...'"

    def compress_old_turns(self, keep_recent: int = 10):
        """Keep only recent turns and carry a compressed summary."""
        if len(self.conversation_history) <= keep_recent:
            return
        
        old = self.conversation_history[:-keep_recent]
        compressed = self._create_compressed_summary(old)
        self.conversation_history = self.conversation_history[-keep_recent:]
        self.context_summary = f"Previous context: {compressed}; {self.context_summary}"

    def generate_session_title(self) -> str:
        """Generate a descriptive title for the session based on conversation content"""
        if not self.conversation_history:
            return "New Chat"

        # Get the first user message for title generation
        first_message = self.conversation_history[0].user_message

        # Clean and truncate the message
        title = first_message.strip()

        # Remove common question words and clean up
        title = title.replace("Can you help me", "").replace("I need help with", "")
        title = title.replace("How do I", "How to").replace("What is", "About")
        title = title.replace("Please", "").replace("Could you", "")

        # Truncate to reasonable length
        if len(title) > 50:
            title = title[:47] + "..."

        # Fallback titles based on content analysis
        if not title.strip():
            topics = self._extract_topics_fast(self.conversation_history[0].user_message)
            if topics:
                title = f"Discussion about {topics[0].replace('_', ' ').title()}"
            else:
                title = f"Chat {self.created_at.strftime('%m/%d')}"

        return title or "Untitled Chat"

    def get_session_summary(self) -> Dict[str, Any]:
        """Get session summary for sidebar display - FIXED VERSION with has_data field"""
        # Use custom title if set, otherwise generate one
        title = getattr(self, 'custom_title', None) or self.generate_session_title()

        # Determine if session has conversation data
        has_conversation_data = len(self.conversation_history) > 0

        return {
            "title": title,
            "last_message_preview": self.conversation_history[-1].user_message[:60] + "..." if self.conversation_history else "No messages",
            "turn_count": len(self.conversation_history),
            "last_activity": self.last_activity.isoformat(),  # Convert to string
            "tools_used": self.total_tools_used,
            "session_id": self.client_session_id,
            "bedrock_session_id": self.bedrock_session_id,
            "is_restored": hasattr(self, 'session_restored') and self.session_restored,
            "has_data": has_conversation_data,  # ✅ FIX: Add the missing has_data field
            "is_discoverable": True  # Active sessions are always discoverable
        }

    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        return {
            "client_session_id": self.client_session_id,
            "bedrock_session_id": self.bedrock_session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "use_bedrock_sessions": self.use_bedrock_sessions,
            "context_summary": self.context_summary,
        }

    def _ensure_invocation(self):
        """Ensure invocation exists for Bedrock persistence."""
        if not self.use_bedrock_sessions:
            logger.info(f"ℹ️  Bedrock sessions disabled, skipping invocation ensure")
            return

        logger.info(f"🔄 _ensure_invocation called for session {self.bedrock_session_id}")
        logger.info(f"   Current invocation_id: {self.current_invocation_id}")

        if not self.current_invocation_id:
            logger.info(f"🔄 No invocation_id, creating new invocation...")
            try:
                self.current_invocation_id = self.bedrock.create_invocation(
                    session_identifier=self.bedrock_session_id,
                    description="mcp-bot conversation",
                )
                if self.current_invocation_id:
                    logger.info(f"✅ Invocation ensured successfully: {self.current_invocation_id}")
                else:
                    logger.error(f"❌ CRITICAL: ensure create_invocation returned None/empty for session {self.bedrock_session_id}")
                    raise RuntimeError(f"ensure create_invocation returned None/empty - this should never happen")
            except Exception as e:
                logger.error(f"❌ CRITICAL: Failed to ensure invocation for session {self.bedrock_session_id}: {e}")
                logger.error(f"   Error type: {type(e).__name__}")
                logger.error(f"   Error details: {str(e)}")
                # Fail fast to expose the exact error instead of silent failure
                raise RuntimeError(f"Required invocation ensure failed: {e}") from e
        else:
            logger.info(f"✅ Invocation already exists: {self.current_invocation_id}")

    def add_turn(self, user_message: str, assistant_response: str, tools_used: Optional[List[Dict]] = None):
        """Add a conversation turn with context tracking; persist to Bedrock steps if enabled."""
        self._validate_turn(user_message, assistant_response, tools_used)
        tools_used = tools_used or []
        now = datetime.now(timezone.utc)
        
        turn = ConversationTurn(
            timestamp=now,
            user_message=user_message,
            assistant_response=assistant_response,
            tools_used=tools_used,
            session_id=self.session_id,
        )
        
        self.conversation_history.append(turn)
        if len(self.conversation_history) > self.config.max_conversation_turns:
            self.compress_old_turns(keep_recent=self.config.compress_keep_recent)
        
        self.last_activity = now
        self.total_tools_used += len(tools_used)
        
        # Persist to Bedrock if enabled
        if self.use_bedrock_sessions:
            self._ensure_invocation()
            if self.current_invocation_id:
                try:
                    # User step
                    self.bedrock.put_invocation_step_structured(
                        session_identifier=self.bedrock_session_id,
                        invocation_identifier=self.current_invocation_id,
                        role="user",
                        text=user_message,
                        tools_used=[],
                        timestamp=now,
                        persist_tools_minimal=False,
                        max_metadata_len=self.config.max_metadata_len,
                    )
                    
                    # Assistant step
                    self.bedrock.put_invocation_step_structured(
                        session_identifier=self.bedrock_session_id,
                        invocation_identifier=self.current_invocation_id,
                        role="assistant",
                        text=assistant_response,
                        tools_used=tools_used,
                        timestamp=now,
                        persist_tools_minimal=self.config.persist_tools_minimal,
                        max_metadata_len=self.config.max_metadata_len,
                    )
                except ClientError as e:
                    logger.error(f"❌ CRITICAL: Bedrock step persistence failed for session {self.bedrock_session_id}: {e}")
                    logger.error(f"   Error type: ClientError")
                    logger.error(f"   Error details: {str(e)}")
                    # Fail fast to expose the exact error instead of silent failure
                    raise RuntimeError(f"Required Bedrock step persistence failed: {e}") from e
                except Exception as e:
                    logger.error(f"❌ CRITICAL: Bedrock step persistence error for session {self.bedrock_session_id}: {e}")
                    logger.error(f"   Error type: {type(e).__name__}")
                    logger.error(f"   Error details: {str(e)}")
                    # Fail fast to expose the exact error instead of silent failure
                    raise RuntimeError(f"Required Bedrock step persistence failed: {e}") from e
        
        self._update_context_summary_optimized()
        logger.info(f"Added turn to session {self.session_id}: {len(tools_used)} tools used")

    def _safe_json_parse(self, json_str: str, fallback=None):
        """Safely parse JSON with error handling."""
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON parse error: {e}, data: {json_str[:100]}")
            return fallback or {}
        except Exception as e:
            logger.error(f"Unexpected JSON parse error: {e}")
            return fallback or {}

    def load_from_bedrock(self, max_steps: int = 50) -> None:
        """Recover conversation history from Bedrock session with loop protection"""
        if not self.use_bedrock_sessions or not self.bedrock:
            logger.info("Bedrock sessions not enabled - skipping history recovery")
            return

        logger.info(f"🔄 Starting Bedrock recovery for session {self.bedrock_session_id}")

        collected = []
        next_token = None
        seen_step_ids = set()  # Track seen steps to prevent duplicates
        loop_count = 0
        max_loops = 20  # Prevent infinite loops

        # Collect all invocation steps with loop protection
        while len(collected) < max_steps and loop_count < max_loops:
            loop_count += 1

            try:
                batch, next_token = self.bedrock.list_invocation_steps(
                    session_identifier=self.bedrock_session_id,
                    max_steps=min(max_steps - len(collected), 50),
                    next_token=next_token
                )

                if not batch:
                    logger.info("No more steps available")
                    break

                # Filter out duplicate steps
                new_steps = []
                for step in batch:
                    step_id = step.get("invocationStepId")
                    if step_id and step_id not in seen_step_ids:
                        seen_step_ids.add(step_id)
                        new_steps.append(step)

                if not new_steps:
                    logger.warning("No new steps found - possible API issue or duplicate data")
                    break

                collected.extend(new_steps)
                logger.info(f"Collected {len(new_steps)} new steps, total: {len(collected)}")

                if not next_token:
                    break

            except Exception as e:
                logger.error(f"Error during step collection (loop {loop_count}): {e}")
                break

        if loop_count >= max_loops:
            logger.error(f"Hit maximum loop limit ({max_loops}) - possible infinite loop detected")

        # Process collected steps
        logger.info(f"Processing {len(collected)} collected steps")

        if len(collected) == 0:
            logger.warning("No invocation steps found - conversation history will be empty")
            return

        temp_turns = {}
        failed_steps = []
        successful_parses = 0

        # Process each step with detailed debugging
        for i, s in enumerate(collected):
            inv_id = s.get("invocationIdentifier") or s.get("invocationId")
            step_id = s.get("invocationStepId")
            step_time = s.get("invocationStepTime")

            if not inv_id or not step_id:
                logger.warning(f"Skipping step {i} - missing invocation ID or step ID")
                failed_steps.append({"step": i, "reason": "missing_ids"})
                continue

            try:
                # Get the step detail - returns the full API response per AWS docs
                detail = self.bedrock.get_invocation_step(
                    session_identifier=self.bedrock_session_id,
                    invocation_identifier=inv_id,
                    invocation_step_id=step_id
                )

                # Debug the complete response structure
                logger.debug(f"Raw step detail for {step_id}: {detail}")

                # CORRECT: Access nested structure as shown in AWS documentation
                if detail and "invocationStep" in detail:
                    invocation_step = detail["invocationStep"]
                    step_payload = invocation_step.get("payload", {})
                    logger.debug(f"Successfully extracted payload with keys: {list(step_payload.keys()) if isinstance(step_payload, dict) else 'Not a dict'}")
                else:
                    logger.warning(f"Step {step_id} missing 'invocationStep' field in response: {list(detail.keys()) if detail else 'No detail'}")
                    failed_steps.append({"step": step_id, "reason": "no_invocation_step_field"})
                    continue

                # Parse the correctly extracted payload
                parsed = self._parse_step_data(step_payload, fallback_time=step_time)

                if not parsed:
                    logger.debug(f"Parsed data empty for step {step_id}")
                    failed_steps.append({"step": step_id, "reason": "empty_parsed_data"})
                    continue

                successful_parses += 1
                logger.debug(f"Successfully parsed step {step_id}: role={parsed.get('role')}, text_len={len(parsed.get('text', ''))}")

            except ClientError as e:
                logger.warning(f"AWS ClientError for step {step_id}: {e}")
                failed_steps.append({"step": step_id, "reason": f"ClientError: {e}"})
                continue
            except Exception as e:
                logger.error(f"Unexpected error processing step {step_id}: {e}")
                failed_steps.append({"step": step_id, "reason": f"Exception: {e}"})
                continue

            # Group by timestamp for user/assistant pairing
            turn_key = parsed.get("timestamp") or step_time
            if turn_key not in temp_turns:
                temp_turns[turn_key] = {"timestamp": parsed.get("timestamp")}

            role = parsed.get("role")
            if role == "user":
                temp_turns[turn_key]["user"] = parsed
            elif role == "assistant":
                temp_turns[turn_key]["assistant"] = parsed

        logger.info(f"Successfully parsed {successful_parses} steps, failed: {len(failed_steps)}")

        # Reconstruct conversation turns
        recovered = 0
        sorted_turns = sorted(temp_turns.values(), key=lambda x: x.get("timestamp", ""))

        logger.info(f"Found {len(sorted_turns)} potential conversation turns")

        for t in sorted_turns:
            if "user" in t and "assistant" in t:
                try:
                    ts_raw = t.get("timestamp") or ""
                    try:
                        # Handle ISO format with Z suffix
                        ts = datetime.fromisoformat(ts_raw.replace("Z", "+00:00")) if ts_raw else datetime.now(timezone.utc)
                    except Exception:
                        ts = datetime.now(timezone.utc)

                    user_text = t["user"].get("text", "")
                    assistant_text = t["assistant"].get("text", "")

                    if user_text and assistant_text:
                        self.conversation_history.append(
                            ConversationTurn(
                                timestamp=ts,
                                user_message=user_text,
                                assistant_response=assistant_text,
                                tools_used=t["assistant"].get("tools_used", []),
                                session_id=self.client_session_id,
                            )
                        )
                        recovered += 1
                        logger.debug(f"Recovered turn: user='{user_text[:50]}...', assistant='{assistant_text[:50]}...'")
                    else:
                        logger.warning(f"Skipping incomplete turn: user_empty={not user_text}, assistant_empty={not assistant_text}")
                except Exception as e:
                    logger.error(f"Error reconstructing conversation turn: {e}")

        if recovered > 0:
            self._update_context_summary_optimized()
            logger.info(f"Successfully recovered {recovered} conversation turns from Bedrock")
        else:
            logger.warning("No conversation turns recovered from Bedrock")
            if failed_steps:
                logger.warning(f"Failed steps summary: {failed_steps}")

    def _parse_step_data(self, payload: Dict[str, Any], fallback_time: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Parse step data from payload with enhanced structure handling"""

        # Debug the payload structure first
        logger.debug(f"Processing payload structure. Type: {type(payload)}")
        logger.debug(f"Payload keys: {list(payload.keys()) if isinstance(payload, dict) else 'Not a dict'}")

        # Handle different payload structures
        blocks = []

        if isinstance(payload, dict):
            if "contentBlocks" in payload and isinstance(payload["contentBlocks"], list):
                blocks = payload["contentBlocks"]
                logger.debug(f"Found contentBlocks with {len(blocks)} blocks")
            else:
                logger.warning(f"Payload structure unexpected. Keys: {list(payload.keys())}")
                # Try alternative structures if needed
                return None
        else:
            logger.warning(f"Payload is not a dict, got {type(payload)}")
            return None

        if not blocks:
            logger.warning("No content blocks found in payload")
            return None

        # Parse the content blocks
        text_content: Optional[str] = None
        role: Optional[str] = None
        ts: Optional[str] = None
        tools_minimal: List[Dict[str, Any]] = []

        logger.debug(f"Processing {len(blocks)} content blocks")

        for i, block in enumerate(blocks):
            logger.debug(f"Block {i}: {block}")

            if "text" in block and isinstance(block["text"], str):
                txt = block["text"]
                logger.debug(f"Block {i} text length: {len(txt)}, starts with: {txt[:20] if txt else 'empty'}")

                # Handle both METADATA and [METADATA] formats based on debug logs
                if txt.startswith("METADATA") or txt.startswith("[METADATA]"):
                    try:
                        # Extract JSON part - handle both formats
                        if txt.startswith("METADATA"):
                            meta_json = txt[len("METADATA"):]
                        else:
                            meta_json = txt[len("[METADATA]"):]

                        meta = self._safe_json_parse(meta_json, {"role": "unknown"})
                        role = role or meta.get("role")
                        ts = ts or meta.get("timestamp")
                        logger.debug(f"Parsed metadata: role={role}, timestamp={ts}")
                    except Exception as e:
                        logger.error(f"Unexpected metadata parsing error: {e}")

                # Handle both TOOLS and [TOOLS] formats
                elif txt.startswith("TOOLS") or txt.startswith("[TOOLS]"):
                    try:
                        # Extract JSON part - handle both formats
                        if txt.startswith("TOOLS"):
                            tools_json = txt[len("TOOLS"):]
                        else:
                            tools_json = txt[len("[TOOLS]"):]

                        tdata = self._safe_json_parse(tools_json, {"tools": []})
                        for t in tdata.get("tools", []):
                            tools_minimal.append({
                                "tool_name": t.get("tool_name"),
                                "server_name": t.get("server_name"),
                                "success": t.get("success"),
                            })
                        logger.debug(f"Parsed {len(tools_minimal)} tools")
                    except Exception as e:
                        logger.error(f"Failed to parse tools JSON: {e}")
                else:
                    # Regular content text
                    if text_content is None:
                        text_content = txt
                        logger.debug(f"Set text content: '{txt[:50]}...'")

        # Validation and result construction
        if not role:
            logger.warning("No role found in parsed data")
        if not text_content:
            logger.warning("No text content found in parsed data")

        if not role and not text_content:
            logger.warning("Both role and text content missing - returning None")
            return None

        if not ts and fallback_time:
            ts = fallback_time
            logger.debug(f"Using fallback timestamp: {ts}")

        result = {
            "role": role,
            "text": text_content or "",
            "timestamp": ts,
            "tools_used": tools_minimal,
        }

        logger.debug(f"Parse result: role={role}, text_length={len(text_content or '')}, tools_count={len(tools_minimal)}")
        return result

    def debug_bedrock_session(self, session_id: str):
        """Debug method to inspect raw Bedrock session data"""
        if not self.bedrock:
            logger.error("No Bedrock backend available for debugging")
            return

        try:
            # Get session details
            session_details = self.bedrock.get_session(session_id)
            logger.info(f"Session Details: {session_details}")

            # List invocation steps
            steps, _ = self.bedrock.list_invocation_steps(session_id, max_steps=10)
            logger.info(f"Found {len(steps)} invocation steps")

            # Inspect first few steps
            for i, step in enumerate(steps[:3]):
                logger.info(f"\nStep {i}: {step}")

                inv_id = step.get("invocationIdentifier")
                step_id = step.get("invocationStepId")

                if inv_id and step_id:
                    try:
                        detail = self.bedrock.get_invocation_step(session_id, inv_id, step_id)
                        logger.info(f"Step detail: {detail}")

                        # Access the correct nested structure per AWS documentation
                        if detail and "invocationStep" in detail:
                            invocation_step = detail["invocationStep"]
                            step_payload = invocation_step.get("payload", {})
                            parsed = self._parse_step_data(step_payload)
                            logger.info(f"Parsed data: {parsed}")
                        else:
                            logger.warning(f"Missing invocationStep in response: {list(detail.keys()) if detail else 'No detail'}")
                    except Exception as e:
                        logger.error(f"Error getting step detail: {e}")

        except Exception as e:
            logger.error(f"Debug failed: {e}")

    def close(self):
        """Properly close the session and clean up resources"""
        if self._is_closed:
            return

        try:
            if self.use_bedrock_sessions and self.bedrock and self.bedrock_session_id:
                logger.info(f"Closing Bedrock session: {self.bedrock_session_id}")
        except Exception as e:
            logger.warning(f"Error closing Bedrock session: {e}")
        finally:
            # Clear references
            self.conversation_history.clear()
            self.bedrock = None
            self._is_closed = True

    def __del__(self):
        """Ensure cleanup on garbage collection"""
        if not self._is_closed:
            self.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        return False  # Don't suppress exceptions

class SessionManager:
    """Manages multiple chat sessions with automatic cleanup and Bedrock lifecycle - BEDROCK ONLY with Discovery"""
    
    def __init__(
        self,
        config: Optional[SessionConfig] = None,
        default_session_metadata: Optional[Dict[str, str]] = None,
        require_bedrock: bool = True,
    ):
        self.config = config or SessionConfig()
        self.config.validate()
        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=self.config.session_timeout_hours)
        
        # BEDROCK REQUIRED MODE
        if require_bedrock:
            try:
                self.backend = BedrockSessionClient(
                    region_name=self.config.region_name,
                    encryption_key_arn=self.config.encryption_key_arn,
                    session_metadata=default_session_metadata,
                )
                logger.info("✅ SessionManager initialized with REQUIRED Bedrock Agent Runtime backend")
            except Exception as e:
                logger.error(f"❌ CRITICAL: Bedrock Agent Runtime is REQUIRED but failed: {e}")
                logger.error("Check: AWS credentials, region, and Bedrock service availability")
                raise RuntimeError(
                    f"Bedrock-only mode configured but Bedrock Agent Runtime unavailable: {e}"
                ) from e
        else:
            # Hybrid mode (for backward compatibility)
            try:
                self.backend = BedrockSessionClient(
                    region_name=self.config.region_name,
                    encryption_key_arn=self.config.encryption_key_arn,
                    session_metadata=default_session_metadata,
                )
                logger.info("SessionManager initialized with Bedrock Agent Runtime backend")
            except Exception as e:
                self.backend = None
                logger.warning(f"Bedrock Agent Runtime backend not available; SessionManager will operate in local-only mode: {e}")
        
        logger.info(f"SessionManager initialized with {self.config.session_timeout_hours}h timeout")

    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a session by ID."""
        return self.sessions.get(session_id)

    def get_or_create_session(self, session_id: Optional[str] = None) -> ChatSession:
        """Get existing session or create new one."""
        if session_id and session_id in self.sessions:
            session = self.sessions[session_id]
            session.last_activity = datetime.now(timezone.utc)
            return session
        
        session = ChatSession(
            session_id=session_id,
            bedrock_backend=self.backend,
            config=self.config,
            require_bedrock=bool(self.backend),
        )
        
        self.sessions[session.session_id] = session
        return session

    def discover_all_sessions(self) -> List[Dict[str, Any]]:
        """Discover all available sessions with enhanced debugging and less restrictive filtering."""
        if not self.backend:
            logger.warning("No Bedrock backend available for session discovery")
            return []

        try:
            logger.info("🔍 Starting session discovery from Bedrock...")

            all_sessions = self.backend.list_all_sessions(max_results=100)  # Increased limit
            logger.info(f"📋 Found {len(all_sessions)} total sessions in Bedrock")

            detailed_sessions = []

            for i, session_summary in enumerate(all_sessions):
                try:
                    session_id = session_summary["sessionId"]
                    logger.debug(f"🔄 Processing session {i+1}/{len(all_sessions)}: {session_id}")

                    # Get conversation preview
                    preview = self._get_conversation_preview(session_id)
                    logger.debug(f"📊 Preview for {session_id}: {preview}")

                    # ✅ LESS RESTRICTIVE FILTERING - Include sessions with turn_count >= 0
                    turn_count = preview.get("turn_count", 0)

                    # Parse last activity time
                    last_activity = session_summary.get("lastUpdatedAt", "")
                    if hasattr(last_activity, 'isoformat'):
                        last_activity = last_activity.isoformat()
                    elif isinstance(last_activity, str) and last_activity:
                        pass
                    else:
                        last_activity = datetime.now(timezone.utc).isoformat()

                    detailed_sessions.append({
                        "session_id": session_id,
                        "title": preview.get("title", f"Session {session_id[:8]}"),
                        "last_message_preview": preview.get("last_message_preview", ""),
                        "turn_count": turn_count,
                        "last_activity": last_activity,
                        "tools_used": preview.get("tools_used_count", 0),
                        "bedrock_session_id": session_id,
                        "is_restored": False,
                        "has_data": turn_count > 0 or preview.get("debug") == "fallback_active_session",  # More generous
                        "is_discoverable": True,
                        "debug_info": preview.get("debug", "")
                    })

                    logger.debug(f"✅ Added session {session_id} with {turn_count} turns")

                except Exception as e:
                    logger.error(f"❌ Failed to process session {session_summary.get('sessionId', 'unknown')}: {e}")
                    # FALLBACK: Still add session with minimal info
                    session_id = session_summary.get("sessionId", f"unknown_{i}")
                    detailed_sessions.append({
                        "session_id": session_id,
                        "title": f"Session {session_id[:8]}",
                        "last_message_preview": "Data available (preview failed)",
                        "turn_count": 1,  # Assume data exists
                        "last_activity": str(session_summary.get("lastUpdatedAt", "")),
                        "tools_used": 0,
                        "bedrock_session_id": session_id,
                        "is_restored": False,
                        "has_data": True,  # Optimistic
                        "is_discoverable": True,
                        "debug_info": f"error: {e}"
                    })
                    continue

            logger.info(f"✅ Session discovery complete: {len(detailed_sessions)} sessions processed")
            return detailed_sessions

        except Exception as e:
            logger.error(f"❌ Session discovery failed: {e}")
            return []

    def _get_conversation_preview(self, session_id: str) -> Dict[str, Any]:
        """Get a preview of conversation with enhanced debugging and fallback logic."""
        try:
            logger.debug(f"🔍 Getting preview for session {session_id}")

            # Get invocation steps
            steps, _ = self.backend.list_invocation_steps(
                session_identifier=session_id,
                max_steps=20  # Increased from 10 to catch more data
            )

            logger.debug(f"📋 Found {len(steps)} invocation steps for {session_id}")

            if not steps:
                logger.debug(f"⚠️ No invocation steps found for session {session_id}")
                # FALLBACK: Try to get session details directly
                try:
                    session_details = self.backend.get_session(session_id)
                    if session_details and session_details.get("sessionStatus") == "ACTIVE":
                        return {
                            "turn_count": 1,  # Assume at least 1 turn if session is active
                            "last_message_preview": "Active session (preview unavailable)",
                            "tools_used_count": 0,
                            "title": f"Active Session {session_id[:8]}",
                            "debug": "fallback_active_session"
                        }
                except Exception as e:
                    logger.debug(f"Fallback failed: {e}")

                return {
                    "turn_count": 0,
                    "last_message_preview": "No steps found",
                    "tools_used_count": 0,
                    "title": f"Empty Session {session_id[:8]}",
                    "debug": "no_steps"
                }

            turn_count = 0
            last_message = ""
            tools_used_count = 0
            parsed_steps = []
            first_user_message = ""

            # Process steps with better error handling
            for i, step in enumerate(reversed(steps)):  # Most recent first
                try:
                    inv_id = step.get("invocationIdentifier")
                    step_id = step.get("invocationStepId")

                    if inv_id and step_id:
                        detail = self.backend.get_invocation_step(
                            session_identifier=session_id,
                            invocation_identifier=inv_id,
                            invocation_step_id=step_id
                        )

                        if detail and "invocationStep" in detail:
                            invocation_step = detail["invocationStep"]
                            step_payload = invocation_step.get("payload", {})

                            # Enhanced step parsing with multiple attempts
                            parsed = self._parse_step_preview_enhanced(step_payload)

                            if parsed:
                                parsed_steps.append(parsed)
                                if parsed.get("role") == "user":
                                    if not first_user_message:  # Get first user message for title
                                        first_user_message = parsed.get("text", "")
                                    if not last_message:  # Get last user message for preview
                                        last_message = parsed.get("text", "")
                                elif parsed.get("role") == "assistant" and not last_message:
                                    last_message = parsed.get("text", "")

                                if parsed.get("tools_used"):
                                    tools_used_count += len(parsed["tools_used"])
                                turn_count += 1
                            else:
                                # Even if parsing fails, count the step
                                turn_count += 1
                                if not last_message:
                                    last_message = f"Step {i+1} (parsing failed)"

                except Exception as e:
                    logger.debug(f"Error processing step {i}: {e}")
                    # Still count failed steps
                    turn_count += 1
                    continue

            # Generate meaningful title
            title = "Conversation"
            if first_user_message:
                title = first_user_message[:50] + "..." if len(first_user_message) > 50 else first_user_message
            elif last_message:
                title = last_message[:50] + "..." if len(last_message) > 50 else last_message
            else:
                title = f"Session {session_id[:8]}"

            # Be more generous with turn counting
            final_turn_count = max(turn_count // 2, 1) if turn_count > 0 else 0

            result = {
                "turn_count": final_turn_count,
                "last_message_preview": last_message or f"Conversation with {len(steps)} steps",
                "tools_used_count": tools_used_count,
                "title": title,
                "debug": {
                    "raw_steps": len(steps),
                    "parsed_steps": len(parsed_steps),
                    "total_turns": turn_count
                }
            }

            logger.debug(f"📊 Enhanced preview result for {session_id}: {result}")
            return result

        except Exception as e:
            logger.error(f"❌ Failed to get conversation preview for {session_id}: {e}")
            # FALLBACK: Return optimistic preview
            return {
                "turn_count": 1,  # Assume conversation exists if session exists
                "last_message_preview": f"Session data available (preview error)",
                "tools_used_count": 0,
                "title": f"Session {session_id[:8]}",
                "debug": f"error: {e}"
            }

    def get_conversation_preview(self, session_id: str) -> Dict[str, Any]:
        """Public method to get conversation preview for API access"""
        return self._get_conversation_preview(session_id)

    def _parse_step_preview(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Quick parse for conversation preview (lighter than full parse)."""
        # 🔍 DEBUG: Log the complete raw payload structure
        logger.debug(f"🔍 RAW PAYLOAD STRUCTURE: {json.dumps(payload, indent=2, default=str)}")

        blocks = payload.get("contentBlocks", [])
        if not blocks:
            return None

        text_content = ""
        role = None
        tools_used = []

        for block in blocks:
            if "text" in block:
                text = block["text"]
                # Handle both METADATA and [METADATA] formats
                if text.startswith("METADATA") or text.startswith("[METADATA]"):
                    try:
                        if text.startswith("METADATA"):
                            meta_json = text[8:]  # len("METADATA")
                        else:
                            meta_json = text[10:]  # len("[METADATA]")
                        meta = json.loads(meta_json)
                        role = meta.get("role")
                    except:
                        pass
                # Handle both TOOLS and [TOOLS] formats
                elif text.startswith("TOOLS") or text.startswith("[TOOLS]"):
                    try:
                        if text.startswith("TOOLS"):
                            tools_json = text[5:]  # len("TOOLS")
                        else:
                            tools_json = text[7:]  # len("[TOOLS]")
                        tools_data = json.loads(tools_json)
                        tools_used = tools_data.get("tools", [])
                    except:
                        pass
                else:
                    text_content = text

        return {
            "role": role,
            "text": text_content,
            "tools_used": tools_used
        }
    def _parse_step_preview_enhanced(self, step_payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Enhanced step parsing with multiple fallback strategies."""
        try:
            # Try the original parsing method first
            result = self._parse_step_preview(step_payload)
            if result:
                return result

            # FALLBACK 1: Direct payload inspection
            if isinstance(step_payload, dict):
                # Look for common patterns in step payload
                text_content = ""
                role = "unknown"
                tools_used = []

                # Try different payload structures
                for key in ["input", "message", "content", "text", "query", "request"]:
                    if key in step_payload and isinstance(step_payload[key], str):
                        text_content = step_payload[key]
                        role = "user"
                        break

                for key in ["output", "response", "result", "answer", "completion"]:
                    if key in step_payload and isinstance(step_payload[key], str):
                        text_content = step_payload[key]
                        role = "assistant"
                        break

                # Look for tool usage indicators
                for key in ["tools", "tool_calls", "function_calls", "actions"]:
                    if key in step_payload:
                        tools_used.append({"name": key, "data": step_payload[key]})

                if text_content:
                    return {
                        "text": text_content,
                        "role": role,
                        "tools_used": tools_used,
                        "source": "enhanced_fallback"
                    }

            # FALLBACK 2: Return generic step indicator
            return {
                "text": f"Step data: {list(step_payload.keys()) if isinstance(step_payload, dict) else str(type(step_payload))}",
                "role": "system",
                "tools_used": [],
                "source": "generic_fallback"
            }

        except Exception as e:
            logger.debug(f"Enhanced step parsing failed: {e}")
            return None

    def discover_sessions(self, search_term: str = "mcp-bot") -> List[Dict[str, Any]]:
        """Discover existing sessions that match search criteria."""
        if not self.backend:
            return []
        
        try:
            # Search for sessions with our app metadata
            discovered = self.backend.search_sessions_by_metadata(search_term)
            
            session_info = []
            for session in discovered:
                session_info.append({
                    "session_id": session["sessionId"],
                    "created_at": session["createdAt"],
                    "last_updated": session["lastUpdatedAt"],
                    "status": session["sessionStatus"],
                    "metadata": session.get("metadata", {}),
                    "in_memory": session["sessionId"] in self.sessions
                })
            
            return session_info
        except Exception as e:
            logger.error(f"Session discovery failed: {e}")
            return []

    def restore_session_by_id(self, session_id: str) -> Optional[ChatSession]:
        """Restore a session by its Bedrock session ID with enhanced logging"""
        if session_id in self.sessions:
            logger.info(f"Session {session_id} already in memory")
            return self.sessions[session_id]

        try:
            if self.backend:
                session_details = self.backend.get_session(session_id)
                logger.info(f"Found Bedrock session details: {session_details}")

                restored_session = ChatSession(
                    session_id=session_id,
                    bedrock_backend=self.backend,
                    config=self.config,
                    require_bedrock=True,
                    existing_bedrock_session_id=session_id
                )

                logger.info(f"Restored session {session_id} with {len(restored_session.conversation_history)} conversation turns")

                # Log first few turns for debugging
                for i, turn in enumerate(restored_session.conversation_history[:3]):
                    logger.debug(f"Turn {i}: user='{turn.user_message[:50]}...', assistant='{turn.assistant_response[:50]}...'")

                self.sessions[session_id] = restored_session
                return restored_session

        except Exception as e:
            logger.error(f"Failed to restore session {session_id}: {e}")
            logger.error(f"Exception details: {type(e).__name__}: {str(e)}")
            return None

    def continue_conversation(self, session_id: str) -> Dict[str, Any]:
        """Prepare session for conversation continuation."""
        try:
            # Restore session if not in memory
            session = self.restore_session_by_id(session_id)
            if not session:
                return {
                    "success": False,
                    "error": f"Could not restore session {session_id}"
                }
            
            # Get context summary for continuation
            context = session.get_context_for_bedrock()
            logger.info(f"Continuing conversation for session {session_id}")
            logger.info(f"Session context: {context}")
            
            # Session is now ready for new messages
            return {
                "success": True,
                "session_restored": True,
                "conversation_turns": len(session.conversation_history),
                "context_summary": session.context_summary,
                "last_activity": session.last_activity.isoformat(),
                "total_tools_used": session.total_tools_used
            }
            
        except Exception as e:
            logger.error(f"Failed to continue conversation for session {session_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        if session_id in self.sessions:
            session = self.sessions[session_id]
            if self.backend and session.bedrock_session_id:
                try:
                    self.backend.end_session(session.bedrock_session_id)
                    if self.config.delete_on_cleanup:
                        self.backend.delete_session(session.bedrock_session_id)
                except ClientError as e:
                    logger.warning(f"Failed to end/delete Bedrock session {session.bedrock_session_id}: {e}")
                except Exception as e:
                    logger.warning(f"Error ending/deleting Bedrock session {session.bedrock_session_id}: {e}")
            
            del self.sessions[session_id]
            logger.info(f"Deleted session: {session_id}")
            return True
        return False

    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        now = datetime.now(timezone.utc)
        expired_sessions = []
        
        for session_id, session in list(self.sessions.items()):
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            session = self.sessions[session_id]
            if self.backend and session.bedrock_session_id:
                try:
                    self.backend.end_session(session.bedrock_session_id)
                    if self.config.delete_on_cleanup:
                        self.backend.delete_session(session.bedrock_session_id)
                except ClientError as e:
                    logger.warning(f"Failed to end/delete Bedrock session {session.bedrock_session_id}: {e}")
                except Exception as e:
                    logger.warning(f"Error ending/deleting Bedrock session {session.bedrock_session_id}: {e}")
            
            del self.sessions[session_id]
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        return len(expired_sessions)

    def get_all_sessions_stats(self) -> Dict[str, Any]:
        """Get statistics for all sessions."""
        total_sessions = len(self.sessions)
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())
        
        active_sessions = 0
        now = datetime.now(timezone.utc)
        for session in self.sessions.values():
            if now - session.last_activity < timedelta(minutes=30):
                active_sessions += 1
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_conversation_turns": total_turns,
            "total_tools_executed": total_tools,
            "session_timeout_hours": self.session_timeout.total_seconds() / 3600,
        }

# Global session manager instance - BEDROCK ONLY MODE
session_manager = SessionManager(
    config=SessionConfig(
        region_name=os.getenv("AWS_REGION", "ap-south-1"),
        encryption_key_arn=None,
        
        session_timeout_hours=2,
        max_conversation_turns=200,
        max_tools_per_turn=25,
        enable_cleanup=True,
        delete_on_cleanup=True,
        recovery_max_steps=200,
        compress_keep_recent=20,
        max_user_message_len=20000,
        max_assistant_response_len=100000,
        max_metadata_len=5000,
        persist_tools_minimal=True,
    ),
    default_session_metadata={"app": "mcp-bot", "env": "prod"},
    require_bedrock=True,
)
