#!/usr/bin/env python3
"""
MCP Server Configuration Script
Provides manual configuration options for MCP servers
"""

import requests
import json
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API base URL
API_BASE = "http://localhost:8000"

# Default server configurations
DEFAULT_SERVERS = [
    {
        "name": "cost-explorer",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            "awslabs.cost-explorer-mcp-server.exe"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Cost Explorer MCP Server for cost analysis and billing insights",
        "enabled": True
    },
    {
        "name": "cloudformation",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-cfn-mcp-server@latest",
            "awslabs.cfn-mcp-server.exe"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1")
        },
        "description": "AWS CloudFormation MCP Server for infrastructure management",
        "enabled": True
    },
    {
        "name": "aws-pricing",
        "command": "uv",
        "args": [
            "tool", "run", "--from",
            "awslabs-aws-pricing-mcp-server@latest",
            "awslabs.aws-pricing-mcp-server.exe"
        ],
        "env": {
            "AWS_PROFILE": os.getenv("AWS_PROFILE", "default"),
            "AWS_REGION": os.getenv("AWS_REGION", "ap-south-1"),
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        "description": "AWS Pricing MCP Server for real-time service pricing information",
        "enabled": True
    }
]

def check_api_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{API_BASE}/")
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False

def configure_server(server_config):
    """Configure a single server"""
    try:
        response = requests.post(
            f"{API_BASE}/servers",
            json=server_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully configured {server_config['name']}")
            return True
        else:
            print(f"❌ Failed to configure {server_config['name']}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error configuring {server_config['name']}: {e}")
        return False

def list_servers():
    """List all configured servers"""
    try:
        response = requests.get(f"{API_BASE}/servers")
        if response.status_code == 200:
            servers = response.json()
            print("\n📋 Configured Servers:")
            print("-" * 50)
            for name, info in servers.items():
                status_emoji = "🟢" if info["status"] == "connected" else "🔴"
                print(f"{status_emoji} {name}: {info['status']} ({info['tools_count']} tools)")
                if info.get('error'):
                    print(f"   Error: {info['error']}")
            return True
        else:
            print(f"❌ Failed to list servers: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error listing servers: {e}")
        return False

def setup_all_servers():
    """Set up all default servers"""
    print("🚀 Setting up default MCP servers...")
    
    success_count = 0
    for server_config in DEFAULT_SERVERS:
        if configure_server(server_config):
            success_count += 1
    
    print(f"\n✨ Setup complete: {success_count}/{len(DEFAULT_SERVERS)} servers configured successfully")
    return success_count == len(DEFAULT_SERVERS)

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python configure_servers.py <command>")
        print("Commands:")
        print("  setup    - Configure all default servers")
        print("  list     - List all configured servers")
        print("  health   - Check API health")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    # Check if API is running
    if not check_api_health():
        print("❌ API is not running. Please start the FastAPI server first:")
        print("   python main.py")
        sys.exit(1)
    
    print("✅ API is running")
    
    if command == "setup":
        setup_all_servers()
    elif command == "list":
        list_servers()
    elif command == "health":
        print("✅ API health check passed")
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
