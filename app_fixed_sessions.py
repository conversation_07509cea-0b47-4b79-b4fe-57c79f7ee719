"""
Enhanced Streamlit Frontend with FIXED Session Loading
Addresses the root cause of blank data despite having 18 sessions with conversation data
"""

import streamlit as st
import requests
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
import os
import traceback
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="Fixed AWS Cost Optimization Assistant",
    page_icon="🔧",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS
st.markdown("""
<style>
.session-item {
    background: #f7f7f8;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    cursor: pointer;
    border-left: 3px solid transparent;
    transition: all 0.2s;
}
.session-item:hover {
    background: #ececf1;
}
.session-item.active {
    background: #d1ecf1;
    border-left-color: #10a37f;
}
.session-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: #374151;
}
.session-preview {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 4px;
}
.session-meta {
    font-size: 11px;
    color: #9ca3af;
    display: flex;
    justify-content: space-between;
}
.success-indicator {
    background: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin: 5px 0;
}
.error-indicator {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin: 5px 0;
}
</style>
""", unsafe_allow_html=True)

class EnhancedAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with enhanced error handling."""
        url = f"{self.base_url}{endpoint}"
        kwargs.setdefault('timeout', self.timeout)
        
        try:
            response = requests.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            st.error(f"API request failed: {e}")
            raise

    def list_sessions_with_titles(self) -> dict:
        """Get sessions with titles and previews"""
        response = self._make_request("GET", "/sessions/list-with-titles")
        return response.json()

    def restore_session(self, session_id: str) -> dict:
        """Restore a session and get its history"""
        response = self._make_request("POST", f"/sessions/restore/{session_id}/continue")
        return response.json()

    def delete_session(self, session_id: str) -> dict:
        """Delete a session"""
        response = self._make_request("DELETE", f"/sessions/{session_id}")
        return response.json()

# Initialize Enhanced API client
@st.cache_resource
def get_enhanced_api_client():
    return EnhancedAPIClient(API_BASE_URL, API_TIMEOUT)

# Enhanced session state initialization
def initialize_enhanced_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"streamlit-{uuid.uuid4().hex[:12]}"
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    if 'session_restored' not in st.session_state:
        st.session_state.session_restored = False

def render_session_sidebar():
    """Render ChatGPT-style session sidebar with FIXED error handling"""
    
    st.sidebar.markdown("### 💬 Chat Sessions")
    
    # New chat button
    if st.sidebar.button("➕ New Chat", use_container_width=True):
        st.session_state.session_id = f"streamlit-{uuid.uuid4().hex[:12]}"
        st.session_state.conversation_history = []
        st.session_state.session_restored = False
        st.success("Started new chat!")
        st.rerun()
    
    # Load sessions
    api_client = get_enhanced_api_client()
    
    try:
        # Add refresh button with debug info
        col1, col2 = st.sidebar.columns([3, 1])
        with col2:
            if st.button("🔄", help="Refresh sessions"):
                st.cache_data.clear()
                st.rerun()
        
        # Cache session loading with detailed error handling
        @st.cache_data(ttl=30, show_spinner=False)
        def load_sessions():
            try:
                response = api_client.list_sessions_with_titles()
                st.sidebar.markdown(f'<div class="success-indicator">✅ API Response: {response.get("success", "Unknown")}</div>', unsafe_allow_html=True)
                return response
            except Exception as e:
                st.sidebar.markdown(f'<div class="error-indicator">❌ API Error: {str(e)}</div>', unsafe_allow_html=True)
                raise
        
        with st.spinner("Loading sessions..."):
            sessions_response = load_sessions()
        
        # Debug information
        st.sidebar.markdown("**Debug Info:**")
        st.sidebar.json({
            "success": sessions_response.get("success", False),
            "count": sessions_response.get("count", 0),
            "has_sessions": bool(sessions_response.get("sessions", [])),
            "api_url": f"{API_BASE_URL}/sessions/list-with-titles"
        })
        
        if not sessions_response.get("success", False):
            st.sidebar.error("❌ API returned success=False")
            return
        
        sessions = sessions_response.get("sessions", [])
        
        if not sessions:
            st.sidebar.warning("⚠️ No sessions returned from API")
            st.sidebar.info("This might indicate:")
            st.sidebar.info("• No sessions with conversation data")
            st.sidebar.info("• Backend filtering issue")
            st.sidebar.info("• DateTime serialization problem")
            return
        
        st.sidebar.success(f"✅ Found {len(sessions)} sessions with conversations")
        
        # Show first session details for debugging
        if sessions:
            with st.sidebar.expander("🔍 First Session Debug", expanded=False):
                st.json(sessions[0])
        
        # Group sessions by date
        today_sessions = []
        older_sessions = []
        
        today = datetime.now().date()
        
        for session in sessions:
            try:
                # Parse the last activity date
                last_activity = session.get("last_activity", "")
                if last_activity and last_activity != "Unknown":
                    # Handle both ISO format and simple date format
                    if "T" in last_activity:
                        session_date = datetime.fromisoformat(last_activity.replace("Z", "+00:00")).date()
                    else:
                        session_date = datetime.strptime(last_activity[:10], "%Y-%m-%d").date()
                    
                    if session_date == today:
                        today_sessions.append(session)
                    else:
                        older_sessions.append(session)
                else:
                    older_sessions.append(session)
            except Exception as e:
                st.sidebar.error(f"Date parsing error: {e}")
                older_sessions.append(session)
        
        # Render session groups
        if today_sessions:
            st.sidebar.markdown("**Today**")
            for session in today_sessions:
                render_session_item(session)
        
        if older_sessions:
            st.sidebar.markdown("**Earlier**")
            for session in older_sessions:
                render_session_item(session)
                
    except Exception as e:
        st.sidebar.error(f"❌ Failed to load sessions: {e}")
        st.sidebar.error(f"Error details: {traceback.format_exc()}")

def render_session_item(session: dict):
    """Render individual session item with enhanced debugging"""
    session_id = session["session_id"]
    title = session["title"]
    preview = session["last_message_preview"]
    turn_count = session["turn_count"]
    tools_used = session["tools_used"]
    last_activity = session["last_activity"]
    has_data = session.get("has_data", False)
    
    # Create expandable session with data indicator (expander doesn't support key parameter)
    data_icon = "💬" if has_data else "📭"
    unique_label = f"{data_icon} {title[:25]}{'...' if len(title) > 25 else ''} ({session_id[:8]})"
    with st.sidebar.expander(unique_label, expanded=False):
        st.markdown(f"""
        **Preview:** {preview[:50]}{'...' if len(preview) > 50 else ''}
        
        **Stats:** {turn_count} turns • {tools_used} tools
        
        **Last Activity:** {last_activity}
        
        **Has Data:** {'✅ Yes' if has_data else '❌ No'}
        """)
        
        # Action buttons (use full session_id with unique prefix to avoid collisions)
        col1, col2 = st.columns(2)

        with col1:
            if st.button("📂 Load", key=f"btn_load_{session_id}", use_container_width=True):
                load_session(session_id)

        with col2:
            if st.button("🗑️ Delete", key=f"btn_delete_{session_id}", use_container_width=True):
                delete_session(session_id)

def load_session(session_id: str):
    """Load a session with enhanced error handling"""
    api_client = get_enhanced_api_client()

    try:
        # Handle mock sessions - use the bedrock_session_id for actual restoration
        if session_id.startswith("mock-"):
            st.warning("⚠️ This is a mock session for UI testing. Real session loading not implemented for mock data.")
            return

        with st.spinner("Loading session..."):
            response = api_client.restore_session(session_id)

        if response.get("success"):
            # Update session state
            st.session_state.session_id = session_id
            st.session_state.conversation_history = []
            st.session_state.session_restored = True

            # Load conversation history
            history = response.get("history", [])
            for turn in history:
                st.session_state.conversation_history.append({
                    "user": turn["user_message"],
                    "assistant": turn["assistant_response"],
                    "timestamp": turn["timestamp"],
                    "tools_used": turn.get("tools_used", [])
                })

            st.success(f"✅ Loaded session with {len(history)} messages!")
            st.cache_data.clear()
            st.rerun()
        else:
            st.error(f"❌ Failed to load session: {response.get('error', 'Unknown error')}")

    except Exception as e:
        st.error(f"❌ Error loading session: {e}")

def delete_session(session_id: str):
    """Delete a session with confirmation"""
    try:
        api_client = get_enhanced_api_client()
        api_client.delete_session(session_id)
        st.success("✅ Session deleted!")
        st.cache_data.clear()
        st.rerun()
    except Exception as e:
        st.error(f"❌ Failed to delete session: {e}")

def main():
    """Main application function with enhanced debugging"""
    initialize_enhanced_session_state()
    
    st.title("🔧 Fixed AWS Cost Optimization Assistant")
    st.markdown("*Debugging session loading issues - Enhanced error handling*")
    
    # API Status Check
    try:
        api_client = get_enhanced_api_client()
        health_response = api_client._make_request("GET", "/")
        health_data = health_response.json()
        
        with st.expander("🔗 API Status", expanded=False):
            st.success("✅ API Connected")
            st.json(health_data)
    except Exception as e:
        st.error(f"❌ API Connection Failed: {e}")
        return
    
    # Render enhanced sidebar
    render_session_sidebar()
    
    # Show current session info
    if st.session_state.session_id:
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            st.caption(f"Session: {st.session_state.session_id[:12]}...")
        with col2:
            st.caption(f"Messages: {len(st.session_state.conversation_history)}")
        with col3:
            if st.session_state.session_restored:
                st.caption("🔄 Restored")
    
    # Display conversation history
    for turn in st.session_state.conversation_history:
        with st.chat_message("user"):
            st.write(turn["user"])
        
        with st.chat_message("assistant"):
            st.write(turn["assistant"])
            
            if turn.get("tools_used"):
                with st.expander(f"🛠️ Tools Used ({len(turn['tools_used'])})", expanded=False):
                    for tool in turn["tools_used"]:
                        st.caption(f"• {tool.get('tool_name', 'Unknown')} ({tool.get('server_name', 'Unknown')})")
    
    # Chat input
    user_input = st.chat_input("Ask about AWS costs, pricing, or infrastructure...")
    
    if user_input:
        # Add user message to display immediately
        with st.chat_message("user"):
            st.write(user_input)
        
        with st.chat_message("assistant"):
            try:
                with st.spinner("Thinking..."):
                    response = api_client._make_request("POST", "/chat", json={
                        "message": user_input,
                        "conversation_id": st.session_state.session_id,
                        "use_tools": True
                    }).json()
                
                st.write(response["response"])
                
                # Store in conversation history
                st.session_state.conversation_history.append({
                    "user": user_input,
                    "assistant": response["response"],
                    "timestamp": datetime.now().isoformat(),
                    "tools_used": response.get("tools_used", [])
                })
                
                st.rerun()
                
            except Exception as e:
                st.error(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
