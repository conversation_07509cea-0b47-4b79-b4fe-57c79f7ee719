#!/usr/bin/env python3
"""
Test script to verify Nova tool name fixes.
"""

import asyncio
import logging
from main_enhanced import EnhancedMCPClientManager

# Set up logging to see debug info
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_nova_tool_name_fixes():
    """Test Nova tool name fixes."""
    manager = EnhancedMCPClientManager()
    
    try:
        logger.info("Testing Nova tool name fixes")
        
        # Test basic conversation without tools first
        session_id = "nova_test_session"
        
        result = await manager.chat_with_bedrock_with_context(
            message="Hello, can you tell me about AWS infrastructure monitoring?",
            session_id=session_id,
            tools_available=[]  # No tools to avoid the error
        )
        
        logger.info("✅ Basic conversation test passed")
        logger.info(f"Response length: {len(result.get('response', ''))}")
        
        # Test tool configuration generation (without actually calling tools)
        available_tools = manager.get_available_tools()
        if available_tools:
            tool_keys = list(available_tools.keys())[:2]  # Get first 2 tools
            logger.info(f"Available tools: {tool_keys}")
            
            # Test tool config generation
            tool_config = manager._build_tool_config_for_bedrock(tool_keys)
            if tool_config:
                logger.info("✅ Tool config generation test passed")
                logger.info(f"Generated {len(tool_config.get('tools', []))} tool configs")
                
                # Check tool name mapping
                if hasattr(manager, '_tool_name_mapping'):
                    logger.info(f"Tool name mapping: {manager._tool_name_mapping}")
                else:
                    logger.warning("No tool name mapping found")
            else:
                logger.info("No tool config generated (no tools available)")
        else:
            logger.info("No tools available for testing")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await manager.cleanup()

if __name__ == "__main__":
    success = asyncio.run(test_nova_tool_name_fixes())
    if success:
        print("✅ Nova tool name fixes test passed!")
    else:
        print("❌ Nova tool name fixes test failed!")
