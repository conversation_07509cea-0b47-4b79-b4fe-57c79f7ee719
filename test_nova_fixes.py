#!/usr/bin/env python3
"""
Test script to verify Nova tool calling fixes.
"""

import asyncio
import logging
from main_enhanced import EnhancedMCPClientManager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_nova_tool_calling():
    """Test Nova tool calling with the fixes."""
    manager = EnhancedMCPClientManager()
    
    try:
        # The manager is already initialized, no need to start
        logger.info("Manager initialized successfully")
        
        # Debug available tools
        available_tools = manager.debug_available_tools()
        logger.info(f"Found {len(available_tools)} available tools")
        
        # Test a simple conversation with tool calling
        session_id = "test_session"
        
        # Get available tool keys for the conversation
        tool_keys = list(available_tools.keys())[:3]  # Use first 3 tools
        logger.info(f"Using tools: {tool_keys}")
        
        # Test the conversation using the enhanced chat method
        result = await manager.chat_with_bedrock_with_context(
            message="What's the current weather like? Use a weather tool if available.",
            session_id=session_id,
            tools_available=tool_keys
        )
        
        logger.info("Conversation completed successfully!")
        logger.info(f"Response: {result.get('response', 'No response')}")
        logger.info(f"Tools used: {len(result.get('tools_used', []))}")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False
    finally:
        await manager.cleanup()

if __name__ == "__main__":
    success = asyncio.run(test_nova_tool_calling())
    if success:
        print("✅ Nova tool calling test passed!")
    else:
        print("❌ Nova tool calling test failed!")
